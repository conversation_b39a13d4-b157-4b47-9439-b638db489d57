import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:geolocator/geolocator.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/services/geofencing/geofencing.service.dart';
import 'package:hrms_tst/core/services/prefs/prefs.protocol.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/home/<USER>/clock_in_out.controller.dart';
import 'package:hrms_tst/features/home/<USER>/work_update.screen.dart';
import 'package:hrms_tst/features/teammates/domain/edit_profile.controller.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/confirmation_dialog.dart';
import 'package:hrms_tst/shared/widgets/loading_placeholder.dart';
import 'package:intl/intl.dart';

import 'package:permission_handler/permission_handler.dart';

class ClockInTile extends HookConsumerWidget {
  const ClockInTile({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(profileDataProvider);

    final clockIn = ref.watch(clockInOutProvider);
    return CardContainer(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            HookBuilder(
              builder: (context) {
                final time = useState(DateTime.now().toLocal());

                useEffect(() {
                  final timer = Timer.periodic(Duration(seconds: 1), (timer) {
                    time.value = DateTime.now().toLocal();
                  });
                  return () => timer.cancel();
                }, []);
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    switch (clockIn) {
                      AsyncData(:final value) => Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _timeSection(
                            context,
                            'Last Clock ${value.clockOut == null ? 'In' : 'Out'}',
                            DateFormat('hh:mm a').format(
                              (value.clockOut ?? value.clockIn!).toLocal(),
                            ),
                          ),

                          Expanded(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Space.x(24),
                                if (value.clockOut == null)
                                  _timeSection(
                                    context,
                                    'Working Hours',
                                    '${(time.value.difference(value.clockIn!.toLocal()).inHours.toString().padLeft(2, '0'))}:${(time.value.difference(value.clockIn!.toLocal()).inMinutes % 60).toString().padLeft(2, '0')}',
                                  ),
                                Spacer(),
                                Text(
                                  DateFormat('hh:mm a').format(time.value),
                                  style: context.textStyles.bodySmall?.copyWith(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: context.colors.secondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      AsyncError(:final error) => Text(error.toString()),

                      _ => Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              LoadingPlaceholder(height: 11, width: 70),
                              Space.y(2),
                              LoadingPlaceholder(height: 14, width: 70),
                            ],
                          ),
                          Space.x(24),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              LoadingPlaceholder(height: 11, width: 70),
                              Space.y(2),
                              LoadingPlaceholder(height: 14, width: 70),
                            ],
                          ),
                          Spacer(),
                          LoadingPlaceholder(height: 16, width: 80),
                        ],
                      ),
                    },

                    Space.y(18),
                    Text(
                      'Hi ${user.firstName}, ${greetingText(time.value)}',
                      style: context.textStyles.titleLarge?.copyWith(
                        fontSize: 24,
                        fontWeight: FontWeight.w500,
                        color: context.colors.secondary,
                      ),
                    ),
                  ],
                );
              },
            ),
            Space.y(18),
            _buildAutoClockInToggle(context, ref),
            Space.y(12),
            clockIn.when(
              skipLoadingOnReload: false,
              data: (value) => ElevatedButton(
                onPressed: () async {
                  if (value.clockOut == null) {
                    context.pushNamed(WorkUpdateScreen.name);
                    // await ref.read(clockInProvider.notifier).clockOut();
                  } else {
                    showAdaptiveDialog(
                      context: context,
                      builder: (dialogContext) => ConfirmationDialog(
                        title: 'Are you sure you want to Clock In?',
                        confirmText: 'Yes',
                        onConfirm: () async {
                          (await ref
                                  .read(clockInOutProvider.notifier)
                                  .clockIn())
                              .fold(
                                onSuccess: (lateMessage) {
                                  if (lateMessage != null) {
                                    showAdaptiveDialog(
                                      context: context,
                                      builder: (context) {
                                        return AlertDialog.adaptive(
                                          title: Text('Late Clock In'),
                                          content: Text(lateMessage),
                                          actions: [
                                            TextButton(
                                              style: Platform.isIOS
                                                  ? TextButton.styleFrom(
                                                      elevation: 0,
                                                      splashFactory: NoSplash
                                                          .splashFactory,
                                                      overlayColor:
                                                          Colors.transparent,
                                                    )
                                                  : TextButton.styleFrom(
                                                      backgroundColor: context
                                                          .colors
                                                          .outline,
                                                    ),
                                              onPressed: () {
                                                context.pop();
                                              },
                                              child: Text('Okay'),
                                            ),
                                          ],
                                        );
                                      },
                                    );
                                  }
                                  context.showSuccess(
                                    'Clocked In Successfully',
                                  );
                                },
                                onFailure: (message) {
                                  context.showError(
                                    message,
                                    message !=
                                            'Can\'t clock in when you are on leave'
                                        ? SnackBarAction(
                                            label: 'Grant',
                                            backgroundColor:
                                                context.colors.surface,
                                            textColor: context.colors.onSurface,
                                            onPressed: () {
                                              openAppSettings();
                                            },
                                          )
                                        : null,
                                  );
                                },
                              );
                        },
                      ),
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 12),
                  shape: ContinuousRectangleBorder(
                    borderRadius: BorderRadiusGeometry.circular(24),
                  ),
                  backgroundColor: value.clockOut == null
                      ? context.colors.error
                      : context.colors.primary,
                ),
                child: Text(
                  'Clock ${value.clockOut == null ? 'Out' : 'In'}',
                  style: context.textStyles.bodyLarge?.copyWith(
                    fontSize: 17,
                    fontWeight: FontWeight.w500,
                    color: context.colors.surface,
                  ),
                ),
              ),
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 12),
                  shape: ContinuousRectangleBorder(
                    borderRadius: BorderRadiusGeometry.circular(24),
                  ),
                ),
                child: Center(
                  child: SizedBox.square(
                    dimension: 25,
                    child: CircularProgressIndicator.adaptive(
                      valueColor: AlwaysStoppedAnimation(
                        context.colors.onPrimary,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAutoClockInToggle(BuildContext context, WidgetRef ref) {
    return HookBuilder(
      builder: (context) {
        // ============================================================================
        // STATE MANAGEMENT
        // ============================================================================
        final isAutoClockInEnabled = useState<bool>(false);
        final hasLocationIssue = useState<bool>(false);
        final statusMessage = useState<String?>(null);
        final isCheckingStatus = useState<bool>(false);
        final isInitialized = useState<bool>(false);

        // ============================================================================
        // INITIALIZATION
        // ============================================================================
        useEffect(() {
          if (!isInitialized.value) {
            _initializeAutoClockInState(
              isAutoClockInEnabled,
              hasLocationIssue,
              statusMessage,
              isInitialized,
            );
          }
          return null;
        }, []);

        // ============================================================================
        // LIFECYCLE OBSERVER
        // ============================================================================
        final lifecycleObserver = useMemoized(
          () => _AutoClockInLifecycleObserver(
            onAppResumed: () => _handleAppResume(
              isAutoClockInEnabled,
              hasLocationIssue,
              statusMessage,
            ),
          ),
        );

        useEffect(() {
          WidgetsBinding.instance.addObserver(lifecycleObserver);
          return () =>
              WidgetsBinding.instance.removeObserver(lifecycleObserver);
        }, []);

        // ============================================================================
        // AUTO CLOCK-IN SUCCESS LISTENER
        // ============================================================================
        useEffect(() {
          log('Auto Clock-In UI Message Port Registered');

          getIt.get<GeofencingService>().eventStream.listen((message) {
            if (context.mounted) {
              if (message.isSuccess) {
                context.showSuccess(message.message);
              } else {
                context.showError(message.message);
              }
            }
          });

          return null;
        }, [isAutoClockInEnabled.value, isInitialized.value]);

        // ============================================================================
        // UI RENDERING
        // ============================================================================

        return AnimatedContainer(
          duration: Duration(milliseconds: 300),
          curve: Curves.ease,
          padding: EdgeInsets.symmetric(vertical: 6, horizontal: 8),
          decoration: ShapeDecoration(
            color: hasLocationIssue.value
                ? context.colors.error.withValues(alpha: 0.2)
                : context.colors.outline,

            shape: ContinuousRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    Icons.location_on,
                    color: hasLocationIssue.value
                        ? context.colors.error
                        : context.colors.primary,
                  ),
                  Space.x(8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Auto Clock-In',
                          style: context.textStyles.labelLarge?.copyWith(
                            fontWeight: FontWeight.w500,
                            color: context.colors.onSurface,
                          ),
                        ),
                        if (statusMessage.value != null)
                          Text(
                            statusMessage.value!,
                            style: context.textStyles.bodySmall?.copyWith(
                              color: hasLocationIssue.value
                                  ? context.colors.error
                                  : context.colors.secondary,
                              fontSize: 12,
                            ),
                          ),
                      ],
                    ),
                  ),
                  Switch(
                    value: isAutoClockInEnabled.value,
                    inactiveTrackColor: context.colors.secondary.withValues(
                      alpha: 0.2,
                    ),
                    onChanged: (value) async {
                      if (value) {
                        // User wants to enable - check for issues first
                        await _checkLocationStatusBeforeEnable(
                          isAutoClockInEnabled,
                          hasLocationIssue,
                          statusMessage,
                          isCheckingStatus,
                          context,
                          ref,
                        );
                      } else {
                        // User wants to disable - allow immediately
                        await _disableAutoClockIn(
                          isAutoClockInEnabled,
                          hasLocationIssue,
                          statusMessage,
                          context,
                          ref,
                        );
                      }
                    },
                  ),
                ],
              ),
              if (hasLocationIssue.value) ...[
                Space.y(8),
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () async {
                          await _handleLocationIssue(
                            hasLocationIssue,
                            statusMessage,
                            isCheckingStatus,
                          );
                        },
                        style: TextButton.styleFrom(
                          backgroundColor: context.colors.surface.withValues(
                            alpha: 0.8,
                          ),
                          padding: EdgeInsets.symmetric(
                            vertical: 8,
                            horizontal: 12,
                          ),
                          shape: ContinuousRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                        child: Text(
                          'Fix Location Issue',
                          style: context.textStyles.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  String greetingText(DateTime time) {
    return switch (time.hour) {
      >= 6 && < 12 => 'Good Morning',
      >= 12 && < 17 => 'Good Afternoon',
      >= 17 && < 21 => 'Good Evening',
      >= 21 || < 6 => 'Good Night',
      _ => 'Good Day',
    };
  }

  Widget _timeSection(BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: context.textStyles.bodySmall?.copyWith(
            fontSize: 8,
            color: context.colors.secondary,
          ),
        ),
        Text(
          value,
          style: context.textStyles.bodySmall?.copyWith(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: context.colors.secondary,
          ),
        ),
      ],
    );
  }

  // ============================================================================
  // AUTO CLOCK-IN STATE MANAGEMENT HELPERS
  // ============================================================================

  /// Initialize auto clock-in state from saved preferences
  Future<void> _initializeAutoClockInState(
    ValueNotifier<bool> isAutoClockInEnabled,
    ValueNotifier<bool> hasLocationIssue,
    ValueNotifier<String?> statusMessage,
    ValueNotifier<bool> isInitialized,
  ) async {
    try {
      final savedValue = await _getAutoClockInSetting();

      isAutoClockInEnabled.value = savedValue;
      isInitialized.value = true;

      // Check location status if auto clock-in is enabled
      if (savedValue) {
        await _checkLocationStatusForEnabledAutoClockIn(
          hasLocationIssue,
          statusMessage,
        );
      }
    } catch (error) {
      isAutoClockInEnabled.value = false;
      isInitialized.value = true;
    }
  }

  /// Handle app resume - check location status if auto clock-in is enabled
  void _handleAppResume(
    ValueNotifier<bool> isAutoClockInEnabled,
    ValueNotifier<bool> hasLocationIssue,
    ValueNotifier<String?> statusMessage,
  ) {
    if (isAutoClockInEnabled.value) {
      _checkLocationStatusForEnabledAutoClockIn(
        hasLocationIssue,
        statusMessage,
      );
    }
  }

  Future<bool> _getAutoClockInSetting() async {
    try {
      final prefs = getIt.get<PrefsProtocol>();
      return await prefs.get<bool>('auto_clock_in_enabled', false) ?? false;
    } catch (e) {
      return false;
    }
  }

  Future<void> _saveAutoClockInSetting(bool enabled) async {
    try {
      final prefs = getIt.get<PrefsProtocol>();
      await prefs.save<bool>('auto_clock_in_enabled', enabled);
    } catch (e) {
      // Handle error silently
    }
  }

  // ============================================================================
  // AUTO CLOCK-IN TOGGLE ACTIONS
  // ============================================================================

  /// Check location status and enable auto clock-in if all requirements are met
  Future<void> _checkLocationStatusBeforeEnable(
    ValueNotifier<bool> isAutoClockInEnabled,
    ValueNotifier<bool> hasLocationIssue,
    ValueNotifier<String?> statusMessage,
    ValueNotifier<bool> isCheckingStatus,
    BuildContext context,
    WidgetRef ref,
  ) async {
    isCheckingStatus.value = true;

    try {
      // Check if location services are enabled
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        hasLocationIssue.value = true;
        statusMessage.value =
            'Location services are disabled. Please enable location services.';
        isCheckingStatus.value = false;

        // Show error snackbar
        if (context.mounted) {
          context.showError(
            'Location services are disabled. Please enable them to use auto clock-in.',
          );
        }
        return;
      }

      // Check and request location permissions
      var permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        // Request permission if denied but not permanently
        statusMessage.value = 'Requesting location permission...';

        // Show info snackbar
        if (context.mounted) {
          context.showInformation(
            'Please grant location permission to enable auto clock-in',
          );
        }

        permission = await Geolocator.requestPermission();
      }

      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        hasLocationIssue.value = true;
        statusMessage.value =
            'Location permission required. Please grant location access.';
        isCheckingStatus.value = false;

        // Show error snackbar
        if (context.mounted) {
          context.showError(
            'Location permission required. Please grant access to use auto clock-in.',
          );
        }
        return;
      }

      // Check and request background location permission
      var backgroundPermission = await Permission.locationAlways.status;
      if (backgroundPermission.isDenied) {
        // Request background location permission if not granted
        statusMessage.value = 'Requesting background location permission...';

        // Show info snackbar
        if (context.mounted) {
          context.showInformation(
            'Please grant "Allow all the time" permission for auto clock-in to work in background',
          );
        }

        backgroundPermission = await Permission.locationAlways.request();
      }

      if (!backgroundPermission.isGranted) {
        hasLocationIssue.value = true;
        statusMessage.value =
            'Background location permission required. Please enable "Allow all the time".';
        isCheckingStatus.value = false;

        // Show error snackbar
        if (context.mounted) {
          context.showError(
            'Background location permission required. Please enable "Allow all the time".',
          );
        }
        return;
      }

      // All checks passed - now setup geofencing
      statusMessage.value = 'Setting up geofencing...';

      final geofenceSetupSuccess = await _setupGeofencing(ref);
      if (!geofenceSetupSuccess) {
        hasLocationIssue.value = true;
        statusMessage.value = 'Failed to setup geofencing. Please try again.';
        isCheckingStatus.value = false;

        // Show error snackbar
        if (context.mounted) {
          context.showError('Failed to setup auto clock-in. Please try again.');
        }
        return;
      }

      // All checks passed and geofencing setup - enable auto clock-in
      isAutoClockInEnabled.value = true;
      await _saveAutoClockInSetting(true);
      hasLocationIssue.value = false;
      statusMessage.value = 'Auto clock-in is enabled and working.';

      // Show success snackbar
      if (context.mounted) {
        context.showSuccess('Auto clock-in enabled successfully!');
      }
    } catch (e) {
      hasLocationIssue.value = true;
      statusMessage.value =
          'Unable to check location status. Please try again.';
    }

    isCheckingStatus.value = false;
  }

  /// Disable auto clock-in and clear any error states
  Future<void> _disableAutoClockIn(
    ValueNotifier<bool> isAutoClockInEnabled,
    ValueNotifier<bool> hasLocationIssue,
    ValueNotifier<String?> statusMessage,
    BuildContext context,
    WidgetRef ref,
  ) async {
    // Remove geofencing when disabling auto clock-in
    await _removeGeofencing(ref);

    isAutoClockInEnabled.value = false;
    await _saveAutoClockInSetting(false);
    hasLocationIssue.value = false;
    statusMessage.value = null;

    // Show info snackbar
    if (context.mounted) {
      context.showSuccess('Auto clock-in disabled');
    }
  }

  // ============================================================================
  // LOCATION STATUS CHECKING
  // ============================================================================

  /// Handle location permission/service issues
  Future<void> _handleLocationIssue(
    ValueNotifier<bool> hasLocationIssue,
    ValueNotifier<String?> statusMessage,
    ValueNotifier<bool> isCheckingStatus,
  ) async {
    // Check current status to determine what action to take
    final serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      await Geolocator.openLocationSettings();
    } else {
      await openAppSettings();
    }

    // Clear the error state temporarily to show user we're checking again
    hasLocationIssue.value = false;
    statusMessage.value =
        'Please check your settings and try enabling auto clock-in again.';
  }

  Future<bool> _setupGeofencing(WidgetRef ref) async {
    try {
      ref
          .read(clockInOutProvider.notifier)
          .setupGeofence(
            onError: (message) {
              throw CustomException(message);
            },
          );
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<void> _removeGeofencing(WidgetRef ref) async {
    try {
      ref.read(clockInOutProvider.notifier).removeGeofence();
    } catch (e) {
      // Silently handle errors when removing geofences
    }
  }

  /// Check location status for enabled auto clock-in (maintains auto clock-in state)
  /// This method is called on app resume to detect if location settings changed
  Future<void> _checkLocationStatusForEnabledAutoClockIn(
    ValueNotifier<bool> hasLocationIssue,
    ValueNotifier<String?> statusMessage,
  ) async {
    try {
      // Check if location services are enabled
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        hasLocationIssue.value = true;
        statusMessage.value =
            'Location services are disabled. Enable location for auto clock-in to work.';
        return;
      }

      // Check location permissions
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        hasLocationIssue.value = true;
        statusMessage.value =
            'Location permission denied. Grant permission for auto clock-in to work.';
        return;
      }

      // Check background location permission
      final backgroundPermission = await Permission.locationAlways.status;
      if (!backgroundPermission.isGranted) {
        hasLocationIssue.value = true;
        statusMessage.value =
            'Background location permission required. Enable "Allow all the time".';
        return;
      }

      // All checks passed - clear any previous issues
      hasLocationIssue.value = false;
      statusMessage.value = 'Auto clock-in is enabled and ready';
    } catch (e) {
      hasLocationIssue.value = true;
      statusMessage.value =
          'Unable to check location status. Auto clock-in may not work properly.';
    }
  }
}

// ============================================================================
// LIFECYCLE OBSERVER FOR AUTO CLOCK-IN
// ============================================================================

/// Lifecycle observer to detect when app resumes from background
class _AutoClockInLifecycleObserver extends WidgetsBindingObserver {
  final VoidCallback onAppResumed;

  _AutoClockInLifecycleObserver({required this.onAppResumed});

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // When app resumes from background, check location status
    if (state == AppLifecycleState.resumed) {
      onAppResumed();
    }
  }
}
