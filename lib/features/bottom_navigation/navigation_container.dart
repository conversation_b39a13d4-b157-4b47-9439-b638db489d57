import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/services/geofencing/geofencing.service.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';

class NavigationContainer extends HookConsumerWidget {
  const NavigationContainer({super.key, required this.navigationShell});

  final StatefulNavigationShell navigationShell;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final color = context.colors.primary;

    final isFirstTime = useState(false);

    // ============================================================================
    // GLOBAL AUTO CLOCK-IN SUCCESS/FAILURE LISTENER
    // ============================================================================
    useEffect(() {
      log('Global Auto Clock-In UI Message Listener Registered');

      final subscription = getIt.get<GeofencingService>().eventStream.listen((
        message,
      ) {
        if (context.mounted) {
          if (message.isSuccess) {
            context.showSuccess(message.message);
          } else {
            context.showError(message.message);
          }
        }
      });

      return () => subscription.cancel();
    }, []);

    return PopScope(
      canPop: isFirstTime.value,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;

        if (!isFirstTime.value) {
          isFirstTime.value = true;
          context.showInformation('Press back again to exit');
          Future.delayed(Duration(seconds: 3), () => isFirstTime.value = false);
        }
      },
      child: Scaffold(
        body: navigationShell,
        bottomNavigationBar: NavigationBar(
          selectedIndex: navigationShell.currentIndex,
          backgroundColor: context.colors.surface,
          elevation: 8,
          shadowColor: context.colors.secondary,
          indicatorColor: Colors.transparent,
          labelPadding: EdgeInsets.zero,
          height: 76,
          overlayColor: WidgetStatePropertyAll(Colors.transparent),
          labelTextStyle: WidgetStateProperty.resolveWith((states) {
            return context.textStyles.titleSmall?.copyWith(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: states.contains(WidgetState.selected)
                  ? context.colors.primary
                  : context.colors.secondary, // Selected label color
            );
          }),
          onDestinationSelected: (index) {
            navigationShell.goBranch(index);
          },
          labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
          destinations: [
            NavigationDestination(
              label: 'Home',
              icon: SVGImage(Assets.svgs.bottomHome),
              selectedIcon: SVGImage(Assets.svgs.bottomHome, color: color),
            ),
            NavigationDestination(
              label: 'Teammates',
              icon: SVGImage(Assets.svgs.bottomTeammates),
              selectedIcon: SVGImage(Assets.svgs.bottomTeammates, color: color),
            ),
            NavigationDestination(
              label: 'Me',
              icon: SVGImage(Assets.svgs.bottomMe),
              selectedIcon: SVGImage(Assets.svgs.bottomMe, color: color),
            ),
            NavigationDestination(
              label: 'Request',
              icon: SVGImage(Assets.svgs.bottomRequest),
              selectedIcon: SVGImage(Assets.svgs.bottomRequest, color: color),
            ),
          ],
        ),
      ),
    );
  }
}
